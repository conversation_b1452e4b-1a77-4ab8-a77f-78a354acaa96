package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
)

// Item represents a simple item structure
type Item struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Description string `json:"description"`
}

// In-memory storage (in production, you'd use a database)
var items []Item
var nextID = 1

// Health check endpoint
func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.<PERSON>er().Set("Content-Type", "application/json")
	response := map[string]string{"status": "healthy", "message": "API is running"}
	json.NewEncoder(w).Encode(response)
}

// GET /items - Get all items
func getItems(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(items)
}

// GET /items/{id} - Get item by ID
func getItem(w http.ResponseWriter, r *http.Request) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid item ID", http.StatusBadRequest)
		return
	}

	for _, item := range items {
		if item.ID == id {
			json.NewEncoder(w).Encode(item)
			return
		}
	}
	http.Error(w, "Item not found", http.StatusNotFound)
}

// POST /items - Create a new item
func createItem(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var item Item
	if err := json.NewDecoder(r.Body).Decode(&item); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	item.ID = nextID
	nextID++
	items = append(items, item)
	
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(item)
}

// PUT /items/{id} - Update an item
func updateItem(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid item ID", http.StatusBadRequest)
		return
	}

	var updatedItem Item
	if err := json.NewDecoder(r.Body).Decode(&updatedItem); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	for i, item := range items {
		if item.ID == id {
			updatedItem.ID = id
			items[i] = updatedItem
			json.NewEncoder(w).Encode(updatedItem)
			return
		}
	}
	http.Error(w, "Item not found", http.StatusNotFound)
}

// DELETE /items/{id} - Delete an item
func deleteItem(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid item ID", http.StatusBadRequest)
		return
	}

	for i, item := range items {
		if item.ID == id {
			items = append(items[:i], items[i+1:]...)
			w.WriteHeader(http.StatusNoContent)
			return
		}
	}
	http.Error(w, "Item not found", http.StatusNotFound)
}

func main() {
	// Initialize with some sample data
	items = []Item{
		{ID: 1, Name: "Sample Item 1", Description: "This is a sample item"},
		{ID: 2, Name: "Sample Item 2", Description: "This is another sample item"},
	}
	nextID = 3

	// Create router
	r := mux.NewRouter()

	// Define routes
	r.HandleFunc("/health", healthCheck).Methods("GET")
	r.HandleFunc("/items", getItems).Methods("GET")
	r.HandleFunc("/items/{id}", getItem).Methods("GET")
	r.HandleFunc("/items", createItem).Methods("POST")
	r.HandleFunc("/items/{id}", updateItem).Methods("PUT")
	r.HandleFunc("/items/{id}", deleteItem).Methods("DELETE")

	// Start server
	port := "8080"
	fmt.Printf("Server starting on port %s...\n", port)
	fmt.Println("Available endpoints:")
	fmt.Println("  GET    /health")
	fmt.Println("  GET    /items")
	fmt.Println("  GET    /items/{id}")
	fmt.Println("  POST   /items")
	fmt.Println("  PUT    /items/{id}")
	fmt.Println("  DELETE /items/{id}")
	
	log.Fatal(http.ListenAndServe(":"+port, r))
}
