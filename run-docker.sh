#!/bin/bash

# Simple script to manage the Go REST API Docker container

CONTAINER_NAME="simple-rest-api"
IMAGE_NAME="simple-rest-api"
PORT="8080"

case "$1" in
    start)
        echo "Starting $CONTAINER_NAME container..."
        docker run -d -p $PORT:$PORT --name $CONTAINER_NAME $IMAGE_NAME
        if [ $? -eq 0 ]; then
            echo "✅ Container started successfully!"
            echo "🌐 API is available at: http://localhost:$PORT"
            echo "🔍 Health check: curl http://localhost:$PORT/health"
        else
            echo "❌ Failed to start container"
        fi
        ;;
    stop)
        echo "Stopping $CONTAINER_NAME container..."
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        echo "✅ Container stopped and removed"
        ;;
    restart)
        echo "Restarting $CONTAINER_NAME container..."
        docker stop $CONTAINER_NAME 2>/dev/null
        docker rm $CONTAINER_NAME 2>/dev/null
        docker run -d -p $PORT:$PORT --name $CONTAINER_NAME $IMAGE_NAME
        if [ $? -eq 0 ]; then
            echo "✅ Container restarted successfully!"
            echo "🌐 API is available at: http://localhost:$PORT"
        else
            echo "❌ Failed to restart container"
        fi
        ;;
    status)
        echo "Checking container status..."
        docker ps -f name=$CONTAINER_NAME
        ;;
    logs)
        echo "Showing container logs..."
        docker logs $CONTAINER_NAME
        ;;
    build)
        echo "Building Docker image..."
        docker build -t $IMAGE_NAME .
        if [ $? -eq 0 ]; then
            echo "✅ Image built successfully!"
        else
            echo "❌ Failed to build image"
        fi
        ;;
    test)
        echo "Testing API endpoints..."
        echo "1. Health check:"
        curl -s http://localhost:$PORT/health | jq . 2>/dev/null || curl -s http://localhost:$PORT/health
        echo -e "\n\n2. Get all items:"
        curl -s http://localhost:$PORT/items | jq . 2>/dev/null || curl -s http://localhost:$PORT/items
        echo -e "\n\n3. Create test item:"
        curl -s -X POST http://localhost:$PORT/items \
            -H "Content-Type: application/json" \
            -d '{"name": "Docker Test Item", "description": "Created from Docker container"}' | \
            jq . 2>/dev/null || curl -s -X POST http://localhost:$PORT/items \
            -H "Content-Type: application/json" \
            -d '{"name": "Docker Test Item", "description": "Created from Docker container"}'
        echo ""
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|build|test}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the API container"
        echo "  stop    - Stop and remove the container"
        echo "  restart - Restart the container"
        echo "  status  - Show container status"
        echo "  logs    - Show container logs"
        echo "  build   - Build the Docker image"
        echo "  test    - Test API endpoints"
        exit 1
        ;;
esac
