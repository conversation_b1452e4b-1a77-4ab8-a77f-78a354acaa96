# Simple REST API in Go

A basic REST API backend built with Go and Gorilla Mux router.

## Features

- Simple CRUD operations for items
- In-memory storage (no database required)
- JSON responses
- Health check endpoint
- Clean REST API design

## Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/items` | Get all items |
| GET | `/items/{id}` | Get item by ID |
| POST | `/items` | Create new item |
| PUT | `/items/{id}` | Update item |
| DELETE | `/items/{id}` | Delete item |

## Getting Started

### Prerequisites
- Go 1.21 or higher

### Installation

1. Install dependencies:
```bash
go mod tidy
```

2. Run the server:
```bash
go run main.go
```

The server will start on `http://localhost:8080`

## API Usage Examples

### Health Check
```bash
curl http://localhost:8080/health
```

### Get All Items
```bash
curl http://localhost:8080/items
```

### Get Item by ID
```bash
curl http://localhost:8080/items/1
```

### Create New Item
```bash
curl -X POST http://localhost:8080/items \
  -H "Content-Type: application/json" \
  -d '{"name": "New Item", "description": "A new item description"}'
```

### Update Item
```bash
curl -X PUT http://localhost:8080/items/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Item", "description": "Updated description"}'
```

### Delete Item
```bash
curl -X DELETE http://localhost:8080/items/1
```

## Item Structure

```json
{
  "id": 1,
  "name": "Item Name",
  "description": "Item Description"
}
```

## Notes

- This uses in-memory storage, so data will be lost when the server restarts
- For production use, consider adding a database (PostgreSQL, MySQL, etc.)
- Error handling is basic but functional
- CORS is not configured (add if needed for frontend integration)
