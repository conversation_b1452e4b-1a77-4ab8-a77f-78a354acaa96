version: '3.8'

services:
  go-rest-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: simple-rest-api
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - api-network

networks:
  api-network:
    driver: bridge
